/**
 * 性能监控器 - 用于监控API响应时间和系统性能
 */
class PerformanceMonitor {
    constructor(options = {}) {
        this.enabled = options.enabled !== false;
        this.thresholds = {
            apiResponse: options.apiResponseThreshold || 5000, // 5秒
            dataProcessing: options.dataProcessingThreshold || 2000, // 2秒
            rendering: options.renderingThreshold || 1000, // 1秒
            ...options.thresholds
        };
        this.metrics = {
            apiCalls: [],
            dataProcessing: [],
            rendering: [],
            memory: [],
            errors: []
        };
        this.timers = new Map();
        this.observers = [];
        
        // 启动内存监控
        if (this.enabled) {
            this.startMemoryMonitoring();
            this.startPerformanceObserver();
        }
    }

    /**
     * 开始计时
     * @param {string} operation - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {Object} 计时器对象
     */
    startTiming(operation, context = {}) {
        if (!this.enabled) return null;

        const timer = {
            operation,
            context,
            startTime: performance.now(),
            startTimestamp: new Date().toISOString(),
            id: `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this.timers.set(timer.id, timer);

        if (context.debug) {
            console.time(`⏱️ ${operation}`);
        }

        return timer;
    }

    /**
     * 结束计时
     * @param {Object} timer - 计时器对象
     * @param {Object} additionalData - 额外数据
     * @returns {number} 执行时间（毫秒）
     */
    endTiming(timer, additionalData = {}) {
        if (!this.enabled || !timer) return 0;

        const endTime = performance.now();
        const duration = endTime - timer.startTime;
        const endTimestamp = new Date().toISOString();

        // 从活动计时器中移除
        this.timers.delete(timer.id);

        const metric = {
            operation: timer.operation,
            context: timer.context,
            startTime: timer.startTime,
            endTime: endTime,
            duration: duration,
            startTimestamp: timer.startTimestamp,
            endTimestamp: endTimestamp,
            ...additionalData
        };

        // 根据操作类型分类存储
        this.categorizeMetric(metric);

        // 检查性能阈值
        this.checkThreshold(metric);

        if (timer.context.debug) {
            console.timeEnd(`⏱️ ${timer.operation}`);
            console.log(`📊 ${timer.operation} 性能指标:`, {
                duration: `${duration.toFixed(2)}ms`,
                threshold: this.getThreshold(timer.operation),
                status: duration > this.getThreshold(timer.operation) ? '⚠️ 超出阈值' : '✅ 正常'
            });
        }

        return duration;
    }

    /**
     * 监控API调用性能
     * @param {string} apiName - API名称
     * @param {Function} apiCall - API调用函数
     * @param {Object} options - 选项
     * @returns {Promise} API调用结果
     */
    async monitorAPICall(apiName, apiCall, options = {}) {
        const timer = this.startTiming(`API_${apiName}`, {
            type: 'api',
            apiName,
            debug: options.debug
        });

        const startMemory = this.getCurrentMemoryUsage();

        try {
            const result = await apiCall();
            const endMemory = this.getCurrentMemoryUsage();
            
            this.endTiming(timer, {
                success: true,
                memoryUsage: {
                    start: startMemory,
                    end: endMemory,
                    delta: endMemory.usedJSHeapSize - startMemory.usedJSHeapSize
                },
                responseSize: this.estimateObjectSize(result)
            });

            return result;
        } catch (error) {
            const endMemory = this.getCurrentMemoryUsage();
            
            this.endTiming(timer, {
                success: false,
                error: error.message,
                memoryUsage: {
                    start: startMemory,
                    end: endMemory,
                    delta: endMemory.usedJSHeapSize - startMemory.usedJSHeapSize
                }
            });

            // 记录错误
            this.recordError(apiName, error, timer.startTime);
            throw error;
        }
    }

    /**
     * 监控数据处理性能
     * @param {string} processName - 处理名称
     * @param {Function} processFunction - 处理函数
     * @param {*} data - 输入数据
     * @param {Object} options - 选项
     * @returns {*} 处理结果
     */
    async monitorDataProcessing(processName, processFunction, data, options = {}) {
        const timer = this.startTiming(`PROCESS_${processName}`, {
            type: 'processing',
            processName,
            inputSize: this.estimateObjectSize(data),
            debug: options.debug
        });

        const startMemory = this.getCurrentMemoryUsage();

        try {
            const result = await processFunction(data);
            const endMemory = this.getCurrentMemoryUsage();
            
            this.endTiming(timer, {
                success: true,
                inputSize: this.estimateObjectSize(data),
                outputSize: this.estimateObjectSize(result),
                memoryUsage: {
                    start: startMemory,
                    end: endMemory,
                    delta: endMemory.usedJSHeapSize - startMemory.usedJSHeapSize
                }
            });

            return result;
        } catch (error) {
            const endMemory = this.getCurrentMemoryUsage();
            
            this.endTiming(timer, {
                success: false,
                error: error.message,
                inputSize: this.estimateObjectSize(data),
                memoryUsage: {
                    start: startMemory,
                    end: endMemory,
                    delta: endMemory.usedJSHeapSize - startMemory.usedJSHeapSize
                }
            });

            this.recordError(processName, error, timer.startTime);
            throw error;
        }
    }

    /**
     * 监控渲染性能
     * @param {string} componentName - 组件名称
     * @param {Function} renderFunction - 渲染函数
     * @param {Object} options - 选项
     * @returns {*} 渲染结果
     */
    async monitorRendering(componentName, renderFunction, options = {}) {
        const timer = this.startTiming(`RENDER_${componentName}`, {
            type: 'rendering',
            componentName,
            debug: options.debug
        });

        try {
            const result = await renderFunction();
            
            this.endTiming(timer, {
                success: true,
                componentName
            });

            return result;
        } catch (error) {
            this.endTiming(timer, {
                success: false,
                error: error.message,
                componentName
            });

            this.recordError(componentName, error, timer.startTime);
            throw error;
        }
    }

    /**
     * 获取性能统计信息
     * @param {string} timeRange - 时间范围（'1h', '24h', 'all'）
     * @returns {Object} 性能统计
     */
    getPerformanceStats(timeRange = '1h') {
        const now = Date.now();
        const timeRangeMs = this.parseTimeRange(timeRange);
        const cutoffTime = now - timeRangeMs;

        const filterByTime = (metrics) => metrics.filter(metric => 
            new Date(metric.startTimestamp).getTime() > cutoffTime
        );

        const apiMetrics = filterByTime(this.metrics.apiCalls);
        const processingMetrics = filterByTime(this.metrics.dataProcessing);
        const renderingMetrics = filterByTime(this.metrics.rendering);
        const errorMetrics = filterByTime(this.metrics.errors);

        return {
            timeRange,
            period: {
                start: new Date(cutoffTime).toISOString(),
                end: new Date(now).toISOString()
            },
            api: this.calculateMetricStats(apiMetrics, 'API调用'),
            dataProcessing: this.calculateMetricStats(processingMetrics, '数据处理'),
            rendering: this.calculateMetricStats(renderingMetrics, '渲染'),
            errors: {
                total: errorMetrics.length,
                byType: this.groupErrorsByType(errorMetrics),
                recent: errorMetrics.slice(-5) // 最近5个错误
            },
            memory: this.getMemoryStats(timeRange),
            thresholdViolations: this.getThresholdViolations(timeRange)
        };
    }

    /**
     * 获取实时性能指标
     * @returns {Object} 实时指标
     */
    getRealTimeMetrics() {
        const activeTimers = Array.from(this.timers.values());
        const currentMemory = this.getCurrentMemoryUsage();
        
        return {
            timestamp: new Date().toISOString(),
            activeOperations: activeTimers.length,
            activeTimers: activeTimers.map(timer => ({
                operation: timer.operation,
                duration: performance.now() - timer.startTime,
                context: timer.context
            })),
            memory: currentMemory,
            performance: {
                navigation: performance.getEntriesByType('navigation')[0],
                paint: performance.getEntriesByType('paint')
            }
        };
    }

    /**
     * 导出性能报告
     * @param {string} format - 导出格式（'json', 'csv', 'html'）
     * @param {string} timeRange - 时间范围
     * @returns {string} 报告内容
     */
    exportReport(format = 'json', timeRange = '24h') {
        const stats = this.getPerformanceStats(timeRange);
        const realTimeMetrics = this.getRealTimeMetrics();

        const report = {
            generatedAt: new Date().toISOString(),
            summary: stats,
            realTime: realTimeMetrics,
            configuration: {
                enabled: this.enabled,
                thresholds: this.thresholds
            }
        };

        switch (format) {
            case 'json':
                return JSON.stringify(report, null, 2);
            
            case 'csv':
                return this.convertToCSV(report);
            
            case 'html':
                return this.convertToHTML(report);
            
            default:
                return JSON.stringify(report, null, 2);
        }
    }

    /**
     * 清理旧的性能数据
     * @param {string} olderThan - 清理多久之前的数据
     */
    cleanup(olderThan = '24h') {
        const cutoffTime = Date.now() - this.parseTimeRange(olderThan);
        
        Object.keys(this.metrics).forEach(key => {
            this.metrics[key] = this.metrics[key].filter(metric => 
                new Date(metric.startTimestamp).getTime() > cutoffTime
            );
        });

        console.log(`🧹 性能监控数据清理完成，保留 ${olderThan} 内的数据`);
    }

    // 私有方法

    /**
     * 启动内存监控
     */
    startMemoryMonitoring() {
        if (typeof window === 'undefined' || !window.performance || !window.performance.memory) {
            return;
        }

        setInterval(() => {
            const memoryInfo = this.getCurrentMemoryUsage();
            this.metrics.memory.push({
                timestamp: new Date().toISOString(),
                ...memoryInfo
            });

            // 限制内存记录数量
            if (this.metrics.memory.length > 1000) {
                this.metrics.memory = this.metrics.memory.slice(-500);
            }
        }, 30000); // 每30秒记录一次
    }

    /**
     * 启动性能观察器
     */
    startPerformanceObserver() {
        if (typeof PerformanceObserver === 'undefined') return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'measure') {
                        // 处理自定义测量
                        this.handleCustomMeasure(entry);
                    }
                });
            });

            observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('性能观察器启动失败:', error);
        }
    }

    /**
     * 分类存储性能指标
     * @param {Object} metric - 性能指标
     */
    categorizeMetric(metric) {
        const { operation, context } = metric;
        
        if (operation.startsWith('API_')) {
            this.metrics.apiCalls.push(metric);
        } else if (operation.startsWith('PROCESS_')) {
            this.metrics.dataProcessing.push(metric);
        } else if (operation.startsWith('RENDER_')) {
            this.metrics.rendering.push(metric);
        }

        // 限制每类指标的数量
        Object.keys(this.metrics).forEach(key => {
            if (this.metrics[key].length > 500) {
                this.metrics[key] = this.metrics[key].slice(-250);
            }
        });
    }

    /**
     * 检查性能阈值
     * @param {Object} metric - 性能指标
     */
    checkThreshold(metric) {
        const threshold = this.getThreshold(metric.operation);
        
        if (metric.duration > threshold) {
            console.warn(`⚠️ 性能阈值超出: ${metric.operation}`, {
                duration: `${metric.duration.toFixed(2)}ms`,
                threshold: `${threshold}ms`,
                context: metric.context
            });

            // 记录阈值违规
            this.recordThresholdViolation(metric, threshold);
        }
    }

    /**
     * 获取操作的性能阈值
     * @param {string} operation - 操作名称
     * @returns {number} 阈值（毫秒）
     */
    getThreshold(operation) {
        if (operation.startsWith('API_')) {
            return this.thresholds.apiResponse;
        } else if (operation.startsWith('PROCESS_')) {
            return this.thresholds.dataProcessing;
        } else if (operation.startsWith('RENDER_')) {
            return this.thresholds.rendering;
        }
        return this.thresholds.apiResponse; // 默认阈值
    }

    /**
     * 记录错误
     * @param {string} operation - 操作名称
     * @param {Error} error - 错误对象
     * @param {number} startTime - 开始时间
     */
    recordError(operation, error, startTime) {
        this.metrics.errors.push({
            operation,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            duration: performance.now() - startTime
        });
    }

    /**
     * 记录阈值违规
     * @param {Object} metric - 性能指标
     * @param {number} threshold - 阈值
     */
    recordThresholdViolation(metric, threshold) {
        if (!this.metrics.thresholdViolations) {
            this.metrics.thresholdViolations = [];
        }

        this.metrics.thresholdViolations.push({
            ...metric,
            threshold,
            violation: metric.duration - threshold,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 获取当前内存使用情况
     * @returns {Object} 内存信息
     */
    getCurrentMemoryUsage() {
        if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
            return {
                usedJSHeapSize: window.performance.memory.usedJSHeapSize,
                totalJSHeapSize: window.performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
            };
        }
        return { usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0 };
    }

    /**
     * 估算对象大小
     * @param {*} obj - 对象
     * @returns {number} 估算大小（字节）
     */
    estimateObjectSize(obj) {
        if (obj === null || obj === undefined) return 0;
        
        try {
            return new Blob([JSON.stringify(obj)]).size;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 解析时间范围
     * @param {string} timeRange - 时间范围字符串
     * @returns {number} 毫秒数
     */
    parseTimeRange(timeRange) {
        const units = {
            'm': 60 * 1000,
            'h': 60 * 60 * 1000,
            'd': 24 * 60 * 60 * 1000
        };

        const match = timeRange.match(/^(\d+)([mhd])$/);
        if (match) {
            const [, value, unit] = match;
            return parseInt(value) * units[unit];
        }

        return 60 * 60 * 1000; // 默认1小时
    }

    /**
     * 计算指标统计
     * @param {Array} metrics - 指标数组
     * @param {string} name - 指标名称
     * @returns {Object} 统计结果
     */
    calculateMetricStats(metrics, name) {
        if (metrics.length === 0) {
            return { name, count: 0 };
        }

        const durations = metrics.map(m => m.duration);
        const successCount = metrics.filter(m => m.success !== false).length;
        
        return {
            name,
            count: metrics.length,
            successRate: ((successCount / metrics.length) * 100).toFixed(1) + '%',
            duration: {
                min: Math.min(...durations).toFixed(2),
                max: Math.max(...durations).toFixed(2),
                avg: (durations.reduce((a, b) => a + b, 0) / durations.length).toFixed(2),
                p95: this.calculatePercentile(durations, 95).toFixed(2)
            }
        };
    }

    /**
     * 计算百分位数
     * @param {Array} values - 数值数组
     * @param {number} percentile - 百分位
     * @returns {number} 百分位数值
     */
    calculatePercentile(values, percentile) {
        const sorted = values.slice().sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[index] || 0;
    }

    /**
     * 按类型分组错误
     * @param {Array} errors - 错误数组
     * @returns {Object} 分组结果
     */
    groupErrorsByType(errors) {
        const grouped = {};
        errors.forEach(error => {
            const type = error.operation || 'Unknown';
            grouped[type] = (grouped[type] || 0) + 1;
        });
        return grouped;
    }

    /**
     * 获取内存统计
     * @param {string} timeRange - 时间范围
     * @returns {Object} 内存统计
     */
    getMemoryStats(timeRange) {
        const cutoffTime = Date.now() - this.parseTimeRange(timeRange);
        const recentMemory = this.metrics.memory.filter(m => 
            new Date(m.timestamp).getTime() > cutoffTime
        );

        if (recentMemory.length === 0) {
            return { available: false };
        }

        const usedHeapSizes = recentMemory.map(m => m.usedJSHeapSize);
        
        return {
            available: true,
            current: this.getCurrentMemoryUsage(),
            trend: {
                min: Math.min(...usedHeapSizes),
                max: Math.max(...usedHeapSizes),
                avg: usedHeapSizes.reduce((a, b) => a + b, 0) / usedHeapSizes.length
            }
        };
    }

    /**
     * 获取阈值违规统计
     * @param {string} timeRange - 时间范围
     * @returns {Array} 违规列表
     */
    getThresholdViolations(timeRange) {
        if (!this.metrics.thresholdViolations) return [];
        
        const cutoffTime = Date.now() - this.parseTimeRange(timeRange);
        return this.metrics.thresholdViolations.filter(v => 
            new Date(v.timestamp).getTime() > cutoffTime
        );
    }

    /**
     * 转换为CSV格式
     * @param {Object} report - 报告对象
     * @returns {string} CSV字符串
     */
    convertToCSV(report) {
        // 简化的CSV转换实现
        const lines = ['Metric,Value'];
        
        if (report.summary.api) {
            lines.push(`API Calls,${report.summary.api.count}`);
            lines.push(`API Success Rate,${report.summary.api.successRate}`);
            lines.push(`API Avg Duration,${report.summary.api.duration?.avg}ms`);
        }
        
        return lines.join('\n');
    }

    /**
     * 转换为HTML格式
     * @param {Object} report - 报告对象
     * @returns {string} HTML字符串
     */
    convertToHTML(report) {
        return `
        <html>
        <head><title>性能监控报告</title></head>
        <body>
            <h1>性能监控报告</h1>
            <p>生成时间: ${report.generatedAt}</p>
            <pre>${JSON.stringify(report, null, 2)}</pre>
        </body>
        </html>
        `;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
} else if (typeof window !== 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
}