// 销售分析模块
class SalesAnalysis {
    constructor() {
        this.charts = {};
        this.data = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.showInitialState();

        // 监听窗口大小变化，重新渲染图表
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                if (this.data) {
                    this.renderDonutCharts();
                    this.renderMonthlyChart();
                }
            }, 300);
        });
    }

    bindEvents() {
        // 搜索按钮事件
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.loadData();
        });

        // 重置按钮事件
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetFilters();
        });

        // 图表类型切换事件
        document.querySelectorAll('input[name="chartType"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.updateMonthlyChart();
            });
        });
    }

    showInitialState() {
        // 清空统计卡片
        document.getElementById('totalProducts').textContent = '0';
        document.getElementById('totalSales').textContent = '0';
        document.getElementById('completionRate').textContent = '0%';
        document.getElementById('yoyGrowth').textContent = '0%';
        document.getElementById('momGrowth').textContent = '0%';

        // 显示空状态的图表
        this.showEmptyCharts();

        // 清空表格
        const tableBody = document.getElementById('productAnalysisBody');
        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666; padding: 40px;">请选择查询条件并点击搜索</td></tr>';
        }
    }

    showEmptyCharts() {
        // 显示空状态的环形图
        ['achievementChart', 'yoyChart', 'momChart', 'shareChart'].forEach(id => {
            const canvas = document.getElementById(id);
            if (canvas) {
                const parent = canvas.parentElement;
                parent.innerHTML = `
                    <canvas id="${id}"></canvas>
                    <div class="chart-label">${this.getChartLabel(id)}</div>
                `;
                // 渲染空的环形图
                this.renderEmptyDonutChart(id);
            }
        });

        // 显示空状态的月度图表
        const monthlyCanvas = document.getElementById('monthlyTrendChart');
        if (monthlyCanvas) {
            const parent = monthlyCanvas.parentElement;
            parent.innerHTML = `
                <canvas id="monthlyTrendChart"></canvas>
                <div style="text-align: center; color: #666; margin-top: 20px;">请选择查询条件并点击搜索</div>
            `;
        }
    }

    getChartLabel(chartId) {
        const labels = {
            'achievementChart': '总体达成率',
            'yoyChart': '同比情况',
            'momChart': '环比情况',
            'shareChart': '占比情况'
        };
        return labels[chartId] || '';
    }

    renderEmptyDonutChart(canvasId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0, 100],
                    backgroundColor: ['#e5e7eb', '#e5e7eb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                elements: {
                    arc: { borderWidth: 0 }
                }
            },
            plugins: [{
                beforeDraw: function(chart) {
                    const ctx = chart.ctx;
                    const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
                    const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

                    ctx.save();
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = '#9ca3af';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillText('--', centerX, centerY - 5);
                    ctx.font = '12px Arial';
                    ctx.fillText('暂无数据', centerX, centerY + 15);
                    ctx.restore();
                }
            }]
        });
    }

    async loadData() {
        try {
            // 获取筛选条件
            const filters = this.getFilters();

            // 显示加载状态
            this.showLoading();

            // 检查是否有token
            const token = localStorage.getItem('token');
            if (!token) {
                // 如果没有token，使用模拟数据
                this.data = this.getMockData();
                this.renderAnalysis();
                return;
            }

            // 调用API获取数据
            const response = await fetch('/api/sales-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(filters)
            });

            if (!response.ok) {
                if (response.status === 401) {
                    // token过期，使用模拟数据
                    this.data = this.getMockData();
                    this.renderAnalysis();
                    return;
                }
                throw new Error('数据加载失败');
            }

            const result = await response.json();
            if (result.success) {
                this.data = result.data;
            } else {
                throw new Error(result.error || '数据格式错误');
            }
            this.renderAnalysis();
        } catch (error) {
            console.error('加载数据失败:', error);
            // 出错时使用模拟数据
            this.data = this.getMockData();
            this.renderAnalysis();
        }
    }

    getMockData() {
        return {
            products: [
                {
                    name: '马来酸依那普利片',
                    actual: 1562258,
                    target: 1466071,
                    lastYearSales: 1367283,
                    lastPeriodSales: 1237631
                },
                {
                    name: '氨氯地平贝那普利片',
                    actual: 434760,
                    target: 483562,
                    lastYearSales: 588964,
                    lastPeriodSales: 494937
                },
                {
                    name: '盐酸贝那普利片',
                    actual: 45529,
                    target: 67622,
                    lastYearSales: 36216,
                    lastPeriodSales: 61598
                },
                {
                    name: '马来酸依那普利叶酸片',
                    actual: 48921,
                    target: 0,
                    lastYearSales: 44396,
                    lastPeriodSales: 55563
                },
                {
                    name: '福辛普利钠片',
                    actual: 67996,
                    target: 332370,
                    lastYearSales: 49362,
                    lastPeriodSales: 27516
                }
            ],
            summary: {
                totalActual: 2159464,
                totalTarget: 2349625,
                totalLastYear: 2086221,
                totalLastPeriod: 1877245
            },
            monthly: [
                { month: '202401', actualAmount: 434760, actualQuantity: 2160, targetAmount: 483562, targetQuantity: 2400 },
                { month: '202402', actualAmount: 45529, actualQuantity: 60, targetAmount: 67622, targetQuantity: 80 },
                { month: '202403', actualAmount: 48921, actualQuantity: 1400, targetAmount: 55000, targetQuantity: 1600 },
                { month: '202404', actualAmount: 67996, actualQuantity: 800, targetAmount: 75000, targetQuantity: 900 },
                { month: '202405', actualAmount: 92000, actualQuantity: 700, targetAmount: 100000, targetQuantity: 800 },
                { month: '202406', actualAmount: 156000, actualQuantity: 3500, targetAmount: 170000, targetQuantity: 3800 }
            ]
        };
    }

    getFilters() {
        // 获取多选筛选器的值
        const getMultiSelectValues = (containerId) => {
            const container = document.getElementById(containerId);
            const checkboxes = container.querySelectorAll('input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value).filter(v => v !== 'all');
        };

        return {
            products: getMultiSelectValues('productOptions'),
            months: getMultiSelectValues('monthOptions'),
            quarters: getMultiSelectValues('quarterOptions'),
            terminals: getMultiSelectValues('terminalOptions'),
            reps: getMultiSelectValues('repOptions')
        };
    }

    showLoading() {
        const tbody = document.getElementById('productAnalysisBody');
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="8" class="loading">加载中...</td></tr>';
        }

        // 清空汇总卡片
        ['totalActual', 'totalTarget', 'totalAchievement', 'totalYoyGrowth', 'totalMomGrowth'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = '-';
        });
    }

    showError(message) {
        const tbody = document.getElementById('productAnalysisBody');
        if (tbody) {
            tbody.innerHTML = `<tr><td colspan="8" class="loading" style="color: #dc2626;">${message}</td></tr>`;
        }
    }

    renderAnalysis() {
        if (!this.data) return;

        console.log('=== 开始渲染分析数据 ===');
        console.log('原始数据:', this.data);

        this.renderProductTable();
        this.renderSummaryCards();
        this.renderDonutCharts();
        this.renderMonthlyChart();
        this.renderBubbleCharts();

        // 显示图表容器
        this.showChartsContainer();

        console.log('=== 分析数据渲染完成 ===');
    }

    renderProductTable() {
        const tbody = document.getElementById('productAnalysisBody');

        if (!tbody) {
            console.error('产品分析表格元素未找到');
            return;
        }

        if (!this.data.products || this.data.products.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="loading">暂无数据</td></tr>';
            return;
        }

        const rows = this.data.products.map(product => {
            // 修改达成率计算逻辑：当指标为0时显示"-"
            let achievementDisplay, achievementColor;
            if (product.target > 0) {
                const achievementRate = (product.actual / product.target * 100).toFixed(1);
                achievementDisplay = achievementRate + '%';
                achievementColor = achievementRate >= 100 ? '#22c55e' : '#ef4444';
            } else {
                achievementDisplay = '-';
                achievementColor = '#9ca3af'; // 灰色
            }

            // 使用后端返回的增长率数据，而不是重新计算
            const yoyGrowth = product.samePeriodGrowth || '0.0%';
            const momGrowth = product.sequentialGrowth || '0.0%';

            return `
                <tr>
                    <td title="${product.name}">${product.name}</td>
                    <td>${this.formatNumber(product.actual)}</td>
                    <td>${this.formatNumber(product.target)}</td>
                    <td style="color: ${achievementColor}">${achievementDisplay}</td>
                    <td>${this.formatNumber(product.lastYearSales)}</td>
                    <td style="color: ${this.getGrowthColor(yoyGrowth)}">${yoyGrowth}</td>
                    <td>${this.formatNumber(product.lastPeriodSales)}</td>
                    <td style="color: ${this.getGrowthColor(momGrowth)}">${momGrowth}</td>
                </tr>
            `;
        }).join('');

        tbody.innerHTML = rows;
    }

    renderSummaryCards() {
        // 基于产品表格数据计算汇总数据
        const tableData = this.calculateTableSummary();

        // 更新汇总卡片
        document.getElementById('totalActual').textContent = this.formatNumber(tableData.totalActual);
        document.getElementById('totalTarget').textContent = this.formatNumber(tableData.totalTarget);

        // 达成率 - 修改逻辑：当总指标为0时显示"-"
        const achievementElement = document.getElementById('totalAchievement');
        if (tableData.totalTarget > 0) {
            const achievementRate = (tableData.totalActual / tableData.totalTarget * 100);
            achievementElement.textContent = achievementRate.toFixed(1) + '%';
            achievementElement.style.color = achievementRate >= 100 ? '#22c55e' : '#ef4444';
        } else {
            achievementElement.textContent = '-';
            achievementElement.style.color = '#9ca3af'; // 灰色
        }

        // 同比增长
        const yoyElement = document.getElementById('totalYoyGrowth');
        if (yoyElement) {
            yoyElement.textContent = (tableData.yoyGrowth >= 0 ? '+' : '') + tableData.yoyGrowth.toFixed(1) + '%';
            yoyElement.style.color = tableData.yoyGrowth >= 0 ? '#22c55e' : '#ef4444';
        }

        // 环比增长
        const momElement = document.getElementById('totalMomGrowth');
        if (momElement) {
            momElement.textContent = (tableData.momGrowth >= 0 ? '+' : '') + tableData.momGrowth.toFixed(1) + '%';
            momElement.style.color = tableData.momGrowth >= 0 ? '#22c55e' : '#ef4444';
        }

        console.log('汇总卡片数据更新:', {
            totalActual: tableData.totalActual,
            totalTarget: tableData.totalTarget,
            achievementRate: achievementRate.toFixed(1) + '%',
            yoyGrowth: tableData.yoyGrowth.toFixed(1) + '%',
            momGrowth: tableData.momGrowth.toFixed(1) + '%'
        });
    }

    renderDonutCharts() {
        if (!this.data.products || this.data.products.length === 0) {
            console.log('没有产品数据，无法渲染图表');
            this.showChartError();
            return;
        }

        // 检查Chart.js是否可用
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，无法渲染图表');
            this.showChartError();
            return;
        }

        // 基于产品表格数据计算汇总数据
        const tableData = this.calculateTableSummary();
        console.log('基于产品表格计算的汇总数据:', tableData);

        // 达成率环形图 - 修改逻辑：当总指标为0时不显示图表
        if (tableData.totalTarget > 0) {
            this.renderDonutChart('achievementChart', {
                value: (tableData.totalActual / tableData.totalTarget * 100),
                label: '达成率',
                color: '#3b82f6'
            });
        } else {
            // 当指标为0时显示空状态
            this.renderEmptyChart('achievementChart', '无指标数据');
        }

        // 同比增长环形图 - 基于产品表格数据，处理null值
        console.log('同期增长环形图渲染:', {
            yoyGrowth: tableData.yoyGrowth,
            isNull: tableData.yoyGrowth === null,
            totalLastYear: tableData.totalLastYear
        });

        if (tableData.yoyGrowth !== null) {
            console.log('渲染同期增长环形图，数值:', tableData.yoyGrowth);
            this.renderDonutChart('yoyChart', {
                value: Math.abs(tableData.yoyGrowth),
                label: tableData.yoyGrowth >= 0 ? '同比增长' : '同比下降',
                color: tableData.yoyGrowth >= 0 ? '#22c55e' : '#ef4444',
                percentage: `${tableData.yoyGrowth >= 0 ? '+' : ''}${tableData.yoyGrowth.toFixed(1)}%`,
                currentValue: tableData.totalActual,
                previousValue: tableData.totalLastYear,
                currentLabel: '当期',
                previousLabel: '同期'
            });
        } else {
            // 当去年同期为0时显示空状态
            console.log('显示空状态：无同期数据');
            this.renderEmptyChart('yoyChart', '无同期数据');
        }

        // 环比增长环形图 - 基于产品表格数据，处理null值
        if (tableData.momGrowth !== null) {
            this.renderDonutChart('momChart', {
                value: Math.abs(tableData.momGrowth),
                label: tableData.momGrowth >= 0 ? '环比增长' : '环比下降',
                color: tableData.momGrowth >= 0 ? '#22c55e' : '#ef4444',
                percentage: `${tableData.momGrowth >= 0 ? '+' : ''}${tableData.momGrowth.toFixed(1)}%`,
                currentValue: tableData.totalActual,
                previousValue: tableData.totalLastPeriod,
                currentLabel: '当期',
                previousLabel: '上期'
            });
        } else {
            // 当上期为0时显示空状态
            this.renderEmptyChart('momChart', '无上期数据');
        }
    }

    // 基于产品表格数据计算汇总数据
    calculateTableSummary() {
        console.log('=== 开始计算表格汇总数据 ===');
        console.log('产品数据:', this.data.products);

        if (!this.data.products || this.data.products.length === 0) {
            console.log('没有产品数据，返回默认值');
            return {
                totalActual: 0,
                totalTarget: 0,
                totalLastYear: 0,
                totalLastPeriod: 0,
                yoyGrowth: 0,
                momGrowth: 0
            };
        }

        // 汇总所有产品的数据
        let totalActual = 0;
        let totalTarget = 0;
        let totalLastYear = 0;
        let totalLastPeriod = 0;

        console.log('开始汇总产品数据:');
        this.data.products.forEach((product, index) => {
            console.log(`产品${index + 1}: ${product.name}`, {
                actual: product.actual || 0,
                target: product.target || 0,
                lastYearSales: product.lastYearSales || 0,
                lastPeriodSales: product.lastPeriodSales || 0
            });

            totalActual += product.actual || 0;
            totalTarget += product.target || 0;
            totalLastYear += product.lastYearSales || 0;
            totalLastPeriod += product.lastPeriodSales || 0;
        });

        // 计算增长率 - 修复逻辑，与后端保持一致
        let yoyGrowth = null;
        let momGrowth = null;

        // 同期增长率计算
        if (totalLastYear === 0) {
            yoyGrowth = null; // 无法计算
        } else if (totalLastYear > 0) {
            yoyGrowth = ((totalActual - totalLastYear) / totalLastYear * 100);
        } else {
            // 去年同期为负数
            if (totalActual >= 0) {
                // 从负数变为正数：改善
                yoyGrowth = ((totalActual - totalLastYear) / Math.abs(totalLastYear) * 100);
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(totalActual) - Math.abs(totalLastYear);
                yoyGrowth = (absChange / Math.abs(totalLastYear)) * 100;
                if (absChange > 0) yoyGrowth = -yoyGrowth; // 亏损增加为负
            }
        }

        // 环比增长率计算
        if (totalLastPeriod === 0) {
            momGrowth = null; // 无法计算
        } else if (totalLastPeriod > 0) {
            momGrowth = ((totalActual - totalLastPeriod) / totalLastPeriod * 100);
        } else {
            // 上期为负数
            if (totalActual >= 0) {
                // 从负数变为正数：改善
                momGrowth = ((totalActual - totalLastPeriod) / Math.abs(totalLastPeriod) * 100);
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(totalActual) - Math.abs(totalLastPeriod);
                momGrowth = (absChange / Math.abs(totalLastPeriod)) * 100;
                if (absChange > 0) momGrowth = -momGrowth; // 亏损增加为负
            }
        }

        const result = {
            totalActual,
            totalTarget,
            totalLastYear,
            totalLastPeriod,
            yoyGrowth,
            momGrowth
        };

        console.log('汇总计算结果:', result);
        console.log('增长率详细信息:', {
            totalActual,
            totalLastYear,
            totalLastPeriod,
            yoyGrowth,
            momGrowth,
            'yoyGrowth是否为null': yoyGrowth === null,
            'momGrowth是否为null': momGrowth === null
        });
        console.log('=== 表格汇总数据计算完成 ===');

        return result;
    }

    showChartError() {
        ['achievementChart', 'yoyChart', 'momChart', 'shareChart', 'monthlyTrendChart'].forEach(id => {
            const canvas = document.getElementById(id);
            if (canvas) {
                const parent = canvas.parentElement;
                parent.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">图表加载失败</div>';
            }
        });
    }

    renderEmptyChart(canvasId, message) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error('Canvas element not found:', canvasId);
            return;
        }

        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }

        // 显示空状态消息
        const parent = canvas.parentElement;
        if (parent) {
            const existingMessage = parent.querySelector('.empty-chart-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'empty-chart-message';
            messageDiv.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #9ca3af;
                font-size: 14px;
                text-align: center;
                z-index: 10;
            `;
            messageDiv.textContent = message;
            parent.style.position = 'relative';
            parent.appendChild(messageDiv);
        }

        // 隐藏canvas
        canvas.style.display = 'none';
    }

    renderDonutChart(canvasId, data) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error('Canvas element not found:', canvasId);
            return;
        }

        // 确保canvas可见并清理空状态消息
        canvas.style.display = 'block';
        const parent = canvas.parentElement;
        if (parent) {
            const existingMessage = parent.querySelector('.empty-chart-message');
            if (existingMessage) {
                existingMessage.remove();
            }
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        // 检测是否为移动设备
        const isMobile = window.innerWidth <= 768;
        const canvasSize = isMobile ? (window.innerWidth <= 480 ? 120 : 150) : 200;

        // 设置canvas尺寸
        canvas.width = canvasSize;
        canvas.height = canvasSize;
        canvas.style.width = canvasSize + 'px';
        canvas.style.height = canvasSize + 'px';

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [Math.min(data.value, 100), Math.max(0, 100 - data.value)],
                    backgroundColor: [data.color, '#e5e7eb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            },
            plugins: [{
                beforeDraw: function(chart) {
                    const width = chart.width;
                    const height = chart.height;
                    const ctx = chart.ctx;

                    ctx.restore();
                    ctx.textBaseline = "middle";
                    ctx.textAlign = "center";
                    ctx.fillStyle = "#333";

                    if (data.percentage && data.currentValue !== undefined && data.previousValue !== undefined) {
                        // 显示详细信息（百分比 + 数值）
                        const mainFontSize = Math.max(14, height / 10);
                        const detailFontSize = Math.max(10, height / 16);

                        // 主要百分比
                        ctx.font = `bold ${mainFontSize}px Arial`;
                        ctx.fillText(data.percentage, width / 2, height / 2 - 15);

                        // 当期数值
                        ctx.font = `${detailFontSize}px Arial`;
                        ctx.fillStyle = "#666";
                        const currentFormatted = new Intl.NumberFormat('zh-CN', {
                            style: 'currency',
                            currency: 'CNY',
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                        }).format(data.currentValue);
                        ctx.fillText(`${data.currentLabel}: ${currentFormatted}`, width / 2, height / 2 + 5);

                        // 对比期数值
                        const previousFormatted = new Intl.NumberFormat('zh-CN', {
                            style: 'currency',
                            currency: 'CNY',
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                        }).format(data.previousValue);
                        ctx.fillText(`${data.previousLabel}: ${previousFormatted}`, width / 2, height / 2 + 20);
                    } else {
                        // 简单显示（只有百分比）
                        const fontSize = Math.max(12, height / 8);
                        ctx.font = `${fontSize}px Arial`;
                        const text = data.value.toFixed(1) + "%";
                        ctx.fillText(text, width / 2, height / 2);
                    }

                    ctx.save();
                }
            }]
        });
    }

    renderMonthlyChart() {
        if (!this.data.monthly) return;

        const canvas = document.getElementById('monthlyTrendChart');
        if (!canvas) {
            console.error('Monthly chart canvas not found');
            return;
        }

        const ctx = canvas.getContext('2d');
        const chartType = document.querySelector('input[name="chartType"]:checked')?.value || 'amount';

        // 销毁现有图表
        if (this.charts.monthlyTrendChart) {
            this.charts.monthlyTrendChart.destroy();
        }

        const monthlyData = this.data.monthly;
        const labels = monthlyData.map(item => {
            // 格式化月份显示
            const year = item.month.substring(0, 4);
            const month = item.month.substring(4, 6);
            return `${year}-${month}`;
        });

        const isMobile = window.innerWidth <= 768;

        const actualData = monthlyData.map(item => chartType === 'amount' ? item.actualAmount : item.actualQuantity);
        const targetData = monthlyData.map(item => chartType === 'amount' ? item.targetAmount : item.targetQuantity);

        // 计算Y轴最大值
        const maxActual = actualData.length > 0 ? Math.max(...actualData) : 0;
        const maxTarget = targetData.length > 0 ? Math.max(...targetData) : 0;
        const maxValue = Math.max(maxActual, maxTarget);

        // 确保Y轴最大值至少有一个合理的值
        let yAxisMax;
        if (maxValue === 0) {
            yAxisMax = 100; // 默认最小值
        } else if (maxValue < 1000) {
            yAxisMax = Math.ceil(maxValue * 1.5); // 小数值时给更多空间
        } else {
            yAxisMax = Math.ceil(maxValue * 1.2);
        }

        const datasets = [{
            label: chartType === 'amount' ? '实际销售额' : '实际销量',
            data: actualData,
            backgroundColor: 'rgba(59, 130, 246, 0.8)',
            borderColor: '#3b82f6',
            borderWidth: 2
        }, {
            label: chartType === 'amount' ? '目标销售额' : '目标销量',
            data: targetData,
            backgroundColor: 'rgba(251, 191, 36, 0.8)',
            borderColor: '#fbbf24',
            borderWidth: 2
        }];

        // 添加达成率线图
        const achievementData = monthlyData.map(item => {
            const actual = chartType === 'amount' ? item.actualAmount : item.actualQuantity;
            const target = chartType === 'amount' ? item.targetAmount : item.targetQuantity;
            return target > 0 ? (actual / target * 100) : 0;
        });

        const maxAchievement = achievementData.length > 0 ? Math.max(...achievementData) : 0;
        const achievementAxisMax = Math.max(120, Math.ceil(maxAchievement * 1.3));

        // 设置canvas高度
        canvas.style.height = isMobile ? '300px' : '400px';

        this.charts.monthlyTrendChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    ...datasets,
                    {
                        label: '达成率',
                        data: achievementData,
                        type: 'line',
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: isMobile ? 45 : 0,
                            font: {
                                size: isMobile ? 10 : 12
                            }
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: !isMobile,
                            text: chartType === 'amount' ? '金额' : '数量'
                        },
                        beginAtZero: true,
                        max: yAxisMax,
                        ticks: {
                            font: {
                                size: isMobile ? 10 : 12
                            },
                            callback: function(value) {
                                // 格式化Y轴标签
                                if (value >= 10000) {
                                    return (value / 10000).toFixed(1) + '万';
                                }
                                return new Intl.NumberFormat('zh-CN', {
                                    minimumFractionDigits: 1,
                                    maximumFractionDigits: 1
                                }).format(value);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: !isMobile,
                            text: '达成率 (%)'
                        },
                        beginAtZero: true,
                        max: achievementAxisMax,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            font: {
                                size: isMobile ? 10 : 12
                            },
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: isMobile ? 'bottom' : 'top',
                        labels: {
                            font: {
                                size: isMobile ? 10 : 12
                            },
                            usePointStyle: true,
                            padding: isMobile ? 10 : 20
                        }
                    },
                    tooltip: {
                        titleFont: {
                            size: isMobile ? 12 : 14
                        },
                        bodyFont: {
                            size: isMobile ? 11 : 13
                        }
                    }
                }
            }
        });
    }

    updateMonthlyChart() {
        this.renderMonthlyChart();
    }

    resetFilters() {
        // 重置所有多选筛选器
        document.querySelectorAll('.multi-select').forEach(select => {
            select.classList.remove('active');
            const checkboxes = select.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            const display = select.querySelector('.multi-select-display .placeholder');
            if (display) {
                display.textContent = display.getAttribute('data-placeholder') || '请选择';
            }
        });

        // 重新加载数据
        this.loadData();
    }

    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return new Intl.NumberFormat('zh-CN', {
            minimumFractionDigits: 1,
            maximumFractionDigits: 1
        }).format(num);
    }

    // 根据增长率文本确定颜色
    getGrowthColor(growthText) {
        if (!growthText || growthText === '-' || growthText === '0.0%') {
            return '#666'; // 灰色
        }

        if (growthText.startsWith('+')) {
            return '#22c55e'; // 绿色 - 正向
        }

        if (growthText.startsWith('-')) {
            return '#ef4444'; // 红色 - 负向
        }

        // 解析数值来判断正负
        const match = growthText.match(/([+-]?\d+\.?\d*)/);
        if (match) {
            const value = parseFloat(match[1]);
            if (value > 0) {
                return '#22c55e'; // 绿色 - 正向
            } else if (value < 0) {
                return '#ef4444'; // 红色 - 负向
            }
        }

        return '#666'; // 默认灰色
    }

    // 生成图表颜色
    generateChartColors(count) {
        const colors = [
            '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
            '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1',
            '#14b8a6', '#f43f5e', '#a855f7', '#10b981', '#f59e0b',
            '#3b82f6', '#ef4444', '#22c55e', '#8b5cf6', '#06b6d4'
        ];

        // 如果数量超过预定义颜色，生成随机颜色
        if (count > colors.length) {
            for (let i = colors.length; i < count; i++) {
                const hue = (i * 137.508) % 360; // 黄金角度分布
                colors.push(`hsl(${hue}, 70%, 50%)`);
            }
        }

        return colors.slice(0, count);
    }

    // 渲染气泡图
    renderBubbleCharts() {
        console.log('=== 开始渲染气泡图 ===');
        console.log('数据检查:', this.data);

        if (!this.data || !this.data.products || this.data.products.length === 0) {
            console.log('没有产品数据，无法渲染气泡图');
            return;
        }

        console.log('产品数据:', this.data.products);

        // 显示气泡图区域
        const bubbleSection = document.getElementById('bubbleChartsSection');
        const bubbleContainer = document.getElementById('bubbleChartsContainer');

        console.log('气泡图区域:', bubbleSection);
        console.log('气泡图容器:', bubbleContainer);

        if (bubbleSection) {
            bubbleSection.style.display = 'block';
            console.log('气泡图区域已显示');
        }

        if (bubbleContainer) {
            bubbleContainer.style.display = 'block';
            console.log('气泡图容器已显示');
        } else {
            console.error('未找到气泡图容器元素');
        }

        // 确保折叠按钮状态正确
        const toggleBtn = document.getElementById('toggleBubbleCharts');
        const toggleText = toggleBtn ? toggleBtn.querySelector('.toggle-text') : null;
        const content = document.getElementById('bubbleChartsContent');

        if (toggleBtn && toggleText && content) {
            // 默认展开状态
            content.classList.remove('collapsed');
            toggleBtn.classList.remove('collapsed');
            toggleText.textContent = '折叠';
            content.style.maxHeight = 'none';

            // 确保标题可见
            const sectionHeader = toggleBtn.closest('.section-header');
            const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;
            if (titleElement) {
                titleElement.style.opacity = '1';
            }
        }

        // 初始化标签页切换
        this.initBubbleChartTabs();

        // 渲染人员气泡图（默认显示）
        this.renderPersonnelBubbleChart();

        // 渲染产品气泡图
        this.renderProductBubbleChart();
    }

    // 初始化气泡图标签页切换
    initBubbleChartTabs() {
        const tabBtns = document.querySelectorAll('.bubble-tab-btn');
        const personnelContainer = document.getElementById('personnelBubbleContainer');
        const productContainer = document.getElementById('productBubbleContainer');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有活动状态
                tabBtns.forEach(b => b.classList.remove('active'));
                // 添加当前按钮的活动状态
                btn.classList.add('active');

                const tab = btn.getAttribute('data-tab');
                if (tab === 'personnel') {
                    personnelContainer.style.display = 'block';
                    productContainer.style.display = 'none';
                } else if (tab === 'product') {
                    personnelContainer.style.display = 'none';
                    productContainer.style.display = 'block';
                }
            });
        });
    }

    // 渲染人员气泡图
    renderPersonnelBubbleChart() {
        const canvas = document.getElementById('personnelBubbleChart');
        if (!canvas) {
            console.error('人员气泡图canvas未找到');
            return;
        }

        // 销毁现有图表
        if (this.charts.personnelBubbleChart) {
            this.charts.personnelBubbleChart.destroy();
        }

        // 准备人员数据（基于产品数据聚合）
        const personnelData = this.preparePersonnelBubbleData();

        const ctx = canvas.getContext('2d');

        console.log('人员气泡图数据:', personnelData);

        // 为每个气泡生成不同的颜色
        const colors = this.generateChartColors(personnelData.length);
        const backgroundColors = colors.map(color => {
            if (color.startsWith('#')) {
                const r = parseInt(color.slice(1, 3), 16);
                const g = parseInt(color.slice(3, 5), 16);
                const b = parseInt(color.slice(5, 7), 16);
                return `rgba(${r}, ${g}, ${b}, 0.6)`;
            } else if (color.startsWith('hsl')) {
                return color.replace('hsl', 'hsla').replace(')', ', 0.6)');
            }
            return color;
        });

        const borderColors = colors.map(color => {
            if (color.startsWith('#')) {
                const r = parseInt(color.slice(1, 3), 16);
                const g = parseInt(color.slice(3, 5), 16);
                const b = parseInt(color.slice(5, 7), 16);
                return `rgba(${r}, ${g}, ${b}, 1)`;
            } else if (color.startsWith('hsl')) {
                return color.replace('hsl', 'hsla').replace(')', ', 1)');
            }
            return color;
        });

        this.charts.personnelBubbleChart = new Chart(ctx, {
            type: 'bubble',
            data: {
                datasets: [{
                    label: '人员表现',
                    data: personnelData,
                    backgroundColor: backgroundColors,
                    borderColor: borderColors,
                    borderWidth: 2
                }]
            },
            options: this.getBubbleChartOptions('人员表现分析')
        });

        console.log('人员气泡图创建完成');
    }

    // 渲染产品气泡图
    renderProductBubbleChart() {
        const canvas = document.getElementById('productBubbleChart');
        if (!canvas) {
            console.error('产品气泡图canvas未找到');
            return;
        }

        // 销毁现有图表
        if (this.charts.productBubbleChart) {
            this.charts.productBubbleChart.destroy();
        }

        // 准备产品数据
        const productData = this.prepareProductBubbleData();

        const ctx = canvas.getContext('2d');

        console.log('产品气泡图数据:', productData);

        // 为每个气泡生成不同的颜色
        const colors = this.generateChartColors(productData.length);
        const backgroundColors = colors.map(color => {
            if (color.startsWith('#')) {
                const r = parseInt(color.slice(1, 3), 16);
                const g = parseInt(color.slice(3, 5), 16);
                const b = parseInt(color.slice(5, 7), 16);
                return `rgba(${r}, ${g}, ${b}, 0.6)`;
            } else if (color.startsWith('hsl')) {
                return color.replace('hsl', 'hsla').replace(')', ', 0.6)');
            }
            return color;
        });

        const borderColors = colors.map(color => {
            if (color.startsWith('#')) {
                const r = parseInt(color.slice(1, 3), 16);
                const g = parseInt(color.slice(3, 5), 16);
                const b = parseInt(color.slice(5, 7), 16);
                return `rgba(${r}, ${g}, ${b}, 1)`;
            } else if (color.startsWith('hsl')) {
                return color.replace('hsl', 'hsla').replace(')', ', 1)');
            }
            return color;
        });

        this.charts.productBubbleChart = new Chart(ctx, {
            type: 'bubble',
            data: {
                datasets: [{
                    label: '产品表现',
                    data: productData,
                    backgroundColor: backgroundColors,
                    borderColor: borderColors,
                    borderWidth: 2
                }]
            },
            options: this.getBubbleChartOptions('产品表现分析')
        });

        console.log('产品气泡图创建完成');
    }

    // 准备人员气泡图数据
    preparePersonnelBubbleData() {
        // 由于当前数据结构中没有人员信息，我们基于产品数据模拟人员数据
        // 在实际应用中，这里应该从后端获取人员相关的数据
        const personnelMap = new Map();

        this.data.products.forEach((product, index) => {
            // 模拟人员名称（实际应用中应该从数据中获取）
            const personnelName = `销售员${String.fromCharCode(65 + (index % 26))}`;

            if (!personnelMap.has(personnelName)) {
                personnelMap.set(personnelName, {
                    name: personnelName,
                    totalSales: 0,
                    totalTarget: 0,
                    totalLastYear: 0,
                    productCount: 0
                });
            }

            const personnel = personnelMap.get(personnelName);
            personnel.totalSales += product.actual || 0;
            personnel.totalTarget += product.target || 0;
            personnel.totalLastYear += product.lastYearSales || 0;
            personnel.productCount += 1;
        });

        return Array.from(personnelMap.values()).map(personnel => {
            const achievementRate = personnel.totalTarget > 0 ?
                (personnel.totalSales / personnel.totalTarget * 100) : 0;
            const growthRate = personnel.totalLastYear > 0 ?
                ((personnel.totalSales - personnel.totalLastYear) / personnel.totalLastYear * 100) : 0;

            // 按您的要求重新映射：
            // X轴：达成率(QP)
            // Y轴：增长率(GR)
            // 气泡大小：销售金额
            const bubbleSize = Math.max(5, Math.min(30, personnel.totalSales / 10000 + 5)); // 基于销售金额

            return {
                x: achievementRate, // X轴：达成率(QP)
                y: growthRate, // Y轴：增长率(GR)
                r: bubbleSize, // 气泡大小：销售金额
                label: personnel.name,
                sales: personnel.totalSales,
                achievement: achievementRate,
                growth: growthRate
            };
        });
    }

    // 准备产品气泡图数据
    prepareProductBubbleData() {
        console.log('=== 销售分析页面：准备产品气泡图数据 ===');
        console.log('产品数据:', this.data.products);

        return this.data.products.map(product => {
            const achievementRate = product.target > 0 ?
                (product.actual / product.target * 100) : 0;

            // 修复增长率计算：使用API返回的比较数据
            let growthRate = 0;
            if (product.samePeriodGrowth) {
                const growthText = product.samePeriodGrowth.toString();
                if (growthText.includes('%')) {
                    const match = growthText.match(/([+-]?\d+\.?\d*)/);
                    growthRate = match ? parseFloat(match[1]) : 0;
                } else if (growthText === '-') {
                    growthRate = 0;
                }
            }

            // 按您的要求重新映射：
            // X轴：达成率(QP)
            // Y轴：增长率(GR)
            // 气泡大小：销售金额 - 以1万为基准单位线性递增
            const salesAmount = product.actual || 0;
            let bubbleSize;

            if (salesAmount === 0) {
                bubbleSize = 8; // 最小气泡
            } else {
                // 以1万为基准单位，每1万增加0.3像素
                // 基础公式：8 + (销售金额 / 10000) * 0.3
                const baseSize = 8; // 基础大小8像素
                const unitAmount = 10000; // 1万为一个单位
                const incrementPerUnit = 0.3; // 每1万增加0.3像素

                bubbleSize = baseSize + (salesAmount / unitAmount) * incrementPerUnit;
            }

            // 确保在合理范围内：最小8像素，最大60像素（避免超过容器）
            bubbleSize = Math.max(8, Math.min(60, bubbleSize));

            const bubbleData = {
                x: achievementRate, // X轴：达成率(QP)
                y: growthRate, // Y轴：增长率(GR)，使用修复后的计算
                r: bubbleSize, // 气泡大小：销售金额
                label: product.name,
                sales: product.actual || 0,
                achievement: achievementRate,
                growth: growthRate
            };

            console.log(`销售分析产品 ${product.name}:`, {
                actual: product.actual,
                target: product.target,
                samePeriodGrowth: product.samePeriodGrowth,
                lastYearSales: product.lastYearSales,
                计算结果: bubbleData
            });

            return bubbleData;
        });
    }

    // 获取气泡图配置选项
    getBubbleChartOptions(title) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return context[0].raw.label;
                        },
                        label: function(context) {
                            const data = context.raw;
                            return [
                                `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                                `达成率: ${data.achievement.toFixed(1)}%`,
                                `同比增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                            ];
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'linear',
                    position: 'bottom',
                    title: {
                        display: true,
                        text: '达成率 (%)',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(0) + '%';
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '同比增长率 (%)',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        callback: function(value) {
                            return (value >= 0 ? '+' : '') + value.toFixed(0) + '%';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'point'
            },
            layout: {
                padding: {
                    top: 40,
                    right: 40,
                    bottom: 80, // 增加底部空间防止文字被遮挡
                    left: 40
                }
            }
        };
    }

    // 显示图表容器
    showChartsContainer() {
        const chartsSection = document.getElementById('chartsSection');
        const chartsContainer = document.getElementById('chartsContainer');

        if (chartsSection) {
            chartsSection.style.display = 'block';
        }

        if (chartsContainer) {
            chartsContainer.style.display = 'grid';
        }
    }
}

// 初始化销售分析
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('sales-content')) {
        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载，等待加载...');
            // 等待Chart.js加载
            const checkChart = setInterval(() => {
                if (typeof Chart !== 'undefined') {
                    clearInterval(checkChart);
                    window.salesAnalysis = new SalesAnalysis();
                }
            }, 100);
        } else {
            window.salesAnalysis = new SalesAnalysis();
        }
    }
});
