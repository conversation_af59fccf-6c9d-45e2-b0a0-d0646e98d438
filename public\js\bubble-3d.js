/**
 * 3D气泡图渲染器
 * 使用Three.js创建3D气泡图可视化
 */

class Bubble3DRenderer {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.bubbles = [];
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.tooltip = null;
        
        this.init();
    }

    init() {
        if (!this.container) {
            console.error(`Container ${this.containerId} not found`);
            return;
        }

        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a2e);

        // 创建相机
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(0, 0, 50);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        this.renderer.setSize(width, height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);

        // 创建控制器
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.enableZoom = true;
            this.controls.enablePan = true;
        } else {
            console.warn('OrbitControls not available, using basic camera controls');
        }

        // 添加光源
        this.addLights();

        // 添加坐标轴
        this.addAxes();

        // 创建tooltip
        this.createTooltip();

        // 添加事件监听
        this.addEventListeners();

        // 创建信息显示面板
        this.createInfoPanel();

        // 开始渲染循环
        this.animate();
    }

    addLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // 点光源
        const pointLight = new THREE.PointLight(0x00ff88, 0.5, 100);
        pointLight.position.set(-30, 30, 30);
        this.scene.add(pointLight);
    }

    addAxes() {
        // 创建坐标轴
        const axesHelper = new THREE.AxesHelper(30);
        this.scene.add(axesHelper);

        // 添加网格
        const gridHelper = new THREE.GridHelper(60, 20, 0x444444, 0x222222);
        gridHelper.rotateX(Math.PI / 2);
        this.scene.add(gridHelper);

        // 添加坐标轴标签
        this.addAxisLabels();
    }

    addAxisLabels() {
        // 添加坐标轴刻度标记
        this.addAxisTicks();

        // 简化版本：使用几何体作为标签指示器
        // X轴标签 (达成率) - 红色
        const xLabelGeometry = new THREE.BoxGeometry(3, 0.8, 0.8);
        const xLabelMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
        const xLabel = new THREE.Mesh(xLabelGeometry, xLabelMaterial);
        xLabel.position.set(25, -3, 0);
        this.scene.add(xLabel);

        // Y轴标签 (同比增长率) - 绿色
        const yLabelGeometry = new THREE.BoxGeometry(0.8, 3, 0.8);
        const yLabelMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const yLabel = new THREE.Mesh(yLabelGeometry, yLabelMaterial);
        yLabel.position.set(-3, 25, 0);
        this.scene.add(yLabel);

        // Z轴标签 - 蓝色
        const zLabelGeometry = new THREE.BoxGeometry(0.8, 0.8, 3);
        const zLabelMaterial = new THREE.MeshBasicMaterial({ color: 0x0000ff });
        const zLabel = new THREE.Mesh(zLabelGeometry, zLabelMaterial);
        zLabel.position.set(0, -3, 25);
        this.scene.add(zLabel);
    }

    addAxisTicks() {
        // 添加X轴刻度 (达成率)
        for (let i = -20; i <= 20; i += 10) {
            const tickGeometry = new THREE.BoxGeometry(0.2, 1, 0.2);
            const tickMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
            const tick = new THREE.Mesh(tickGeometry, tickMaterial);
            tick.position.set(i, -1.5, 0);
            this.scene.add(tick);
        }

        // 添加Y轴刻度 (同比增长率)
        for (let i = -15; i <= 15; i += 7.5) {
            const tickGeometry = new THREE.BoxGeometry(1, 0.2, 0.2);
            const tickMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
            const tick = new THREE.Mesh(tickGeometry, tickMaterial);
            tick.position.set(-1.5, i, 0);
            this.scene.add(tick);
        }
    }

    createTooltip() {
        this.tooltip = document.createElement('div');
        this.tooltip.style.position = 'absolute';
        this.tooltip.style.padding = '10px';
        this.tooltip.style.background = 'rgba(0, 0, 0, 0.8)';
        this.tooltip.style.color = 'white';
        this.tooltip.style.borderRadius = '5px';
        this.tooltip.style.pointerEvents = 'none';
        this.tooltip.style.display = 'none';
        this.tooltip.style.zIndex = '1001';
        this.tooltip.style.fontSize = '12px';
        this.tooltip.style.maxWidth = '200px';

        // 将tooltip附加到容器而不是body，确保定位正确
        this.container.appendChild(this.tooltip);
    }

    addEventListeners() {
        // 鼠标移动事件
        this.renderer.domElement.addEventListener('mousemove', (event) => {
            this.onMouseMove(event);
        });

        // 双击事件
        this.renderer.domElement.addEventListener('dblclick', (event) => {
            this.onDoubleClick(event);
        });

        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }

    onMouseMove(event) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 射线检测
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.bubbles);

        if (intersects.length > 0) {
            const bubble = intersects[0].object;
            const data = bubble.userData;

            // 显示tooltip - 使用相对于容器的定位
            const rect = this.container.getBoundingClientRect();
            this.tooltip.style.display = 'block';
            this.tooltip.style.left = (event.clientX - rect.left + 10) + 'px';
            this.tooltip.style.top = (event.clientY - rect.top - 10) + 'px';
            this.tooltip.innerHTML = `
                <strong>${data.label}</strong><br>
                销售金额: ¥${(data.sales / 10000).toFixed(1)}万<br>
                达成率: ${data.achievement.toFixed(1)}%<br>
                同比增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%
                ${data.productCount !== undefined ? `<br>负责产品: ${data.productCount}个` : ''}
            `;

            // 更新信息面板
            this.updateHoverInfo(data);

            // 高亮效果
            bubble.material.emissive.setHex(0x444444);
        } else {
            this.tooltip.style.display = 'none';
            this.hideHoverInfo();
            // 移除高亮
            this.bubbles.forEach(bubble => {
                bubble.material.emissive.setHex(0x000000);
            });
        }
    }

    onDoubleClick(event) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 射线检测
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.bubbles);

        if (intersects.length > 0) {
            const data = intersects[0].object.userData;
            console.log('3D气泡双击:', data.label, '类型:', data.type);

            // 根据数据类型调用不同的弹窗函数
            if (data.type === 'personnel') {
                showPersonnelProductBubbleModal(data.label);
            } else if (data.type === 'product') {
                showProductHospitalBubbleModal(data.label);
            } else if (data.type === 'hospital') {
                showHospitalProductBubbleModal(data.label);
            }
        }
    }

    onWindowResize() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    clearBubbles() {
        // 清除现有气泡
        this.bubbles.forEach(bubble => {
            this.scene.remove(bubble);
        });
        this.bubbles = [];
    }

    renderBubbles(data, title = '3D气泡图') {
        this.clearBubbles();

        if (!data || data.length === 0) {
            console.log('没有数据可渲染');
            // 添加一个测试立方体确保渲染器工作
            this.addTestCube();
            return;
        }

        console.log('开始渲染3D气泡，数据数量:', data.length);

        // 计算数据范围用于归一化
        const maxSales = Math.max(...data.map(d => d.sales));
        const minSales = Math.min(...data.map(d => d.sales));
        const maxAchievement = Math.max(...data.map(d => d.achievement));
        const minAchievement = Math.min(...data.map(d => d.achievement));
        const maxGrowth = Math.max(...data.map(d => d.growth));
        const minGrowth = Math.min(...data.map(d => d.growth));

        console.log('数据范围:', {
            sales: [minSales, maxSales],
            achievement: [minAchievement, maxAchievement],
            growth: [minGrowth, maxGrowth]
        });

        data.forEach((item, index) => {
            // 按您的要求重新映射：
            // X轴：达成率
            const x = maxAchievement > minAchievement ?
                ((item.achievement - minAchievement) / (maxAchievement - minAchievement) - 0.5) * 40 : 0;

            // Y轴：同比增长率
            const y = maxGrowth > minGrowth ?
                ((item.growth - minGrowth) / (maxGrowth - minGrowth) - 0.5) * 30 : 0;

            // Z轴：随机分布增加3D层次
            const z = (Math.random() - 0.5) * 20;

            // 气泡大小：按金额
            const radius = maxSales > minSales ?
                Math.max(0.8, ((item.sales - minSales) / (maxSales - minSales)) * 3 + 0.5) : 1;

            // 颜色基于同比增长率
            const color = item.growth >= 0 ? 
                new THREE.Color().setHSL(0.3, 0.8, 0.6) : // 绿色表示正增长
                new THREE.Color().setHSL(0.0, 0.8, 0.6);   // 红色表示负增长

            // 创建气泡几何体
            const geometry = new THREE.SphereGeometry(radius, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: color,
                transparent: true,
                opacity: 0.8,
                shininess: 100
            });

            const bubble = new THREE.Mesh(geometry, material);
            bubble.position.set(x, y, z);
            bubble.castShadow = true;
            bubble.receiveShadow = true;

            // 存储数据用于tooltip
            bubble.userData = item;

            console.log(`创建气泡 ${index}:`, {
                label: item.label,
                position: { x, y, z },
                radius: radius,
                sales: item.sales,
                achievement: item.achievement,
                growth: item.growth
            });

            this.scene.add(bubble);
            this.bubbles.push(bubble);

            // 添加动画效果
            const targetScale = 1;
            bubble.scale.set(0, 0, 0);
            
            // 使用简单的动画
            const animateScale = () => {
                if (bubble.scale.x < targetScale) {
                    bubble.scale.x += 0.05;
                    bubble.scale.y += 0.05;
                    bubble.scale.z += 0.05;
                    requestAnimationFrame(animateScale);
                }
            };
            
            setTimeout(() => animateScale(), index * 100);
        });

        console.log(`渲染了 ${data.length} 个3D气泡`);
    }

    // 添加测试立方体
    addTestCube() {
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshPhongMaterial({ color: 0x00ff00 });
        const cube = new THREE.Mesh(geometry, material);
        cube.position.set(0, 0, 0);
        this.scene.add(cube);
        this.bubbles.push(cube);
        console.log('添加了测试立方体');
    }

    // 创建信息显示面板 - 已移除说明，仅保留悬停信息功能
    createInfoPanel() {
        // 为每个3D渲染器创建唯一的信息面板ID
        const hoverInfoId = `bubble-hover-info-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // 确保容器有相对定位
        this.container.style.position = 'relative';

        // 保存悬停信息元素的ID，供后续使用
        this.hoverInfoId = hoverInfoId;
    }

    // 更新悬停信息 - 现在使用tooltip显示
    updateHoverInfo(data) {
        // 悬停信息现在通过tooltip显示，不需要额外处理
    }

    // 隐藏悬停信息
    hideHoverInfo() {
        // 悬停信息现在通过tooltip显示，不需要额外处理
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // 更新控制器
        if (this.controls) {
            this.controls.update();
        }

        // 添加气泡旋转动画
        this.bubbles.forEach((bubble, index) => {
            bubble.rotation.y += 0.01;
            bubble.position.y += Math.sin(Date.now() * 0.001 + index) * 0.02;
        });

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }

    destroy() {
        if (this.tooltip) {
            document.body.removeChild(this.tooltip);
        }
        
        if (this.renderer) {
            this.container.removeChild(this.renderer.domElement);
            this.renderer.dispose();
        }
        
        this.clearBubbles();
    }
}

// 全局3D渲染器实例
let personnel3DRenderer = null;
let product3DRenderer = null;
