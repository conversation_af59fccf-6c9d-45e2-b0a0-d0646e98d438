/**
 * 数据验证器 - 用于验证多维分析数据的一致性和完整性
 */
class DataValidator {
    constructor(options = {}) {
        this.debugMode = options.debugMode || false;
        this.strictMode = options.strictMode || false;
    }

    /**
     * 验证产品分析数据的一致性
     * @param {Array} products - 产品数据数组
     * @returns {Object} 验证结果
     */
    validateProductAnalysisData(products) {
        const errors = [];
        const warnings = [];
        const validProducts = [];

        if (!Array.isArray(products)) {
            errors.push('产品数据必须是数组格式');
            return { isValid: false, errors, warnings, validProducts };
        }

        products.forEach((product, index) => {
            const productErrors = [];
            const productWarnings = [];

            // 检查必要字段
            const requiredFields = ['name', 'actual', 'target'];
            const missingFields = requiredFields.filter(field => 
                product[field] === undefined || product[field] === null
            );

            if (missingFields.length > 0) {
                productErrors.push(`产品 ${product.name || `索引${index}`} 缺少必要字段: ${missingFields.join(', ')}`);
            }

            // 检查数据类型
            if (product.actual !== undefined && typeof product.actual !== 'number') {
                productErrors.push(`产品 ${product.name} 的实际销售额必须是数字类型`);
            }

            if (product.target !== undefined && typeof product.target !== 'number') {
                productErrors.push(`产品 ${product.name} 的目标金额必须是数字类型`);
            }

            if (product.lastYearSales !== undefined && typeof product.lastYearSales !== 'number') {
                productErrors.push(`产品 ${product.name} 的同期销售额必须是数字类型`);
            }

            if (product.lastPeriodSales !== undefined && typeof product.lastPeriodSales !== 'number') {
                productErrors.push(`产品 ${product.name} 的环比销售额必须是数字类型`);
            }

            // 检查增长率格式
            if (product.samePeriodGrowth !== undefined && 
                typeof product.samePeriodGrowth !== 'string') {
                productErrors.push(`产品 ${product.name} 的同期增长率必须是字符串格式`);
            }

            if (product.sequentialGrowth !== undefined && 
                typeof product.sequentialGrowth !== 'string') {
                productErrors.push(`产品 ${product.name} 的环比增长率必须是字符串格式`);
            }

            // 检查数据逻辑一致性
            const consistencyResult = this.checkDataConsistency(product);
            if (!consistencyResult.isConsistent) {
                productErrors.push(...consistencyResult.errors);
                productWarnings.push(...consistencyResult.warnings);
            }

            // 检查数值合理性
            const reasonabilityResult = this.checkDataReasonability(product);
            if (!reasonabilityResult.isReasonable) {
                productWarnings.push(...reasonabilityResult.warnings);
            }

            // 记录产品级别的错误和警告
            if (productErrors.length > 0) {
                errors.push(...productErrors);
            }
            if (productWarnings.length > 0) {
                warnings.push(...productWarnings);
            }

            // 如果没有严重错误，添加到有效产品列表
            if (productErrors.length === 0) {
                validProducts.push(product);
            }
        });

        const isValid = errors.length === 0;

        if (this.debugMode) {
            console.group('数据验证结果');
            console.log('验证通过:', isValid);
            console.log('有效产品数量:', validProducts.length);
            console.log('错误数量:', errors.length);
            console.log('警告数量:', warnings.length);
            if (errors.length > 0) {
                console.error('错误详情:', errors);
            }
            if (warnings.length > 0) {
                console.warn('警告详情:', warnings);
            }
            console.groupEnd();
        }

        return { 
            isValid, 
            errors, 
            warnings, 
            validProducts,
            totalProducts: products.length,
            validProductCount: validProducts.length
        };
    }

    /**
     * 检查数据逻辑一致性
     * @param {Object} product - 产品数据
     * @returns {Object} 一致性检查结果
     */
    checkDataConsistency(product) {
        const errors = [];
        const warnings = [];

        // 检查负数值
        if (product.actual < 0) {
            warnings.push(`产品 ${product.name} 的实际销售额为负数: ${product.actual}`);
        }

        if (product.target < 0) {
            warnings.push(`产品 ${product.name} 的目标金额为负数: ${product.target}`);
        }

        // 检查达成率逻辑
        if (product.actual > 0 && product.target > 0) {
            const calculatedRate = (product.actual / product.target * 100).toFixed(1);
            // 这里可以添加达成率的合理性检查
            if (calculatedRate > 1000) {
                warnings.push(`产品 ${product.name} 的达成率异常高: ${calculatedRate}%`);
            }
        }

        // 检查增长率格式
        if (product.samePeriodGrowth && !this.isValidGrowthRate(product.samePeriodGrowth)) {
            errors.push(`产品 ${product.name} 的同期增长率格式错误: ${product.samePeriodGrowth}`);
        }

        if (product.sequentialGrowth && !this.isValidGrowthRate(product.sequentialGrowth)) {
            errors.push(`产品 ${product.name} 的环比增长率格式错误: ${product.sequentialGrowth}`);
        }

        return {
            isConsistent: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 检查数据合理性
     * @param {Object} product - 产品数据
     * @returns {Object} 合理性检查结果
     */
    checkDataReasonability(product) {
        const warnings = [];

        // 检查异常大的数值
        const maxReasonableValue = 10000000; // 1000万
        if (product.actual > maxReasonableValue) {
            warnings.push(`产品 ${product.name} 的实际销售额异常大: ${product.actual}`);
        }

        if (product.target > maxReasonableValue) {
            warnings.push(`产品 ${product.name} 的目标金额异常大: ${product.target}`);
        }

        // 检查异常的增长率
        if (product.samePeriodGrowth && product.samePeriodGrowth !== '-') {
            const growthValue = this.parseGrowthRate(product.samePeriodGrowth);
            if (Math.abs(growthValue) > 1000) {
                warnings.push(`产品 ${product.name} 的同期增长率异常: ${product.samePeriodGrowth}`);
            }
        }

        if (product.sequentialGrowth && product.sequentialGrowth !== '-') {
            const growthValue = this.parseGrowthRate(product.sequentialGrowth);
            if (Math.abs(growthValue) > 1000) {
                warnings.push(`产品 ${product.name} 的环比增长率异常: ${product.sequentialGrowth}`);
            }
        }

        return {
            isReasonable: warnings.length === 0,
            warnings
        };
    }

    /**
     * 验证增长率格式是否正确
     * @param {string} growthRate - 增长率字符串
     * @returns {boolean} 是否有效
     */
    isValidGrowthRate(growthRate) {
        if (growthRate === '-') return true;
        
        // 匹配格式：+123.4% 或 -123.4% 或 123.4%
        const growthPattern = /^[+-]?\d+(\.\d+)?%$/;
        return growthPattern.test(growthRate);
    }

    /**
     * 解析增长率字符串为数值
     * @param {string} growthRate - 增长率字符串
     * @returns {number} 增长率数值
     */
    parseGrowthRate(growthRate) {
        if (growthRate === '-') return 0;
        
        const numericPart = growthRate.replace(/[+%]/g, '');
        return parseFloat(numericPart) || 0;
    }

    /**
     * 验证API响应数据结构
     * @param {Object} response - API响应数据
     * @returns {Object} 验证结果
     */
    validateAPIResponse(response) {
        const errors = [];
        const warnings = [];

        // 检查基本结构
        if (!response || typeof response !== 'object') {
            errors.push('API响应数据格式错误');
            return { isValid: false, errors, warnings };
        }

        if (response.success === undefined) {
            errors.push('API响应缺少success字段');
        }

        if (response.success && !response.data) {
            errors.push('API响应成功但缺少data字段');
        }

        if (response.data && !response.data.products) {
            warnings.push('API响应数据中缺少products字段');
        }

        if (response.data && response.data.products && !Array.isArray(response.data.products)) {
            errors.push('API响应中的products字段必须是数组');
        }

        // 检查汇总数据
        if (response.data && response.data.summary) {
            const summary = response.data.summary;
            const requiredSummaryFields = ['totalActual', 'totalTarget'];
            
            requiredSummaryFields.forEach(field => {
                if (summary[field] === undefined) {
                    warnings.push(`汇总数据缺少${field}字段`);
                }
            });
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证特定产品的数据（如美思信）
     * @param {Object} product - 产品数据
     * @param {string} expectedName - 期望的产品名称
     * @returns {Object} 验证结果
     */
    validateSpecificProduct(product, expectedName) {
        const errors = [];
        const warnings = [];

        if (!product) {
            errors.push(`未找到产品 ${expectedName} 的数据`);
            return { isValid: false, errors, warnings };
        }

        if (product.name !== expectedName) {
            errors.push(`产品名称不匹配，期望: ${expectedName}, 实际: ${product.name}`);
        }

        // 特殊验证逻辑（可以根据具体产品定制）
        if (expectedName === '美思信') {
            // 美思信的特殊验证规则
            if (product.actual > 0 && product.lastYearSales > 0) {
                const expectedGrowthRate = ((product.actual - product.lastYearSales) / product.lastYearSales * 100);
                const actualGrowthRate = this.parseGrowthRate(product.samePeriodGrowth);
                
                if (Math.abs(expectedGrowthRate - actualGrowthRate) > 0.1) {
                    warnings.push(`美思信同期增长率计算可能有误，期望: ${expectedGrowthRate.toFixed(1)}%, 实际: ${product.samePeriodGrowth}`);
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataValidator;
} else if (typeof window !== 'undefined') {
    window.DataValidator = DataValidator;
}