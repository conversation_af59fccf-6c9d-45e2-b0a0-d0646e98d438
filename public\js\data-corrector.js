/**
 * 数据修复器 - 用于处理字段映射和数据类型问题
 */
class DataCorrector {
    constructor(options = {}) {
        this.debugMode = options.debugMode || false;
        this.autoCorrect = options.autoCorrect !== false;
        this.fieldMappings = {
            // 标准字段映射
            actual: ['actual', 'actualAmount', 'actualSales'],
            target: ['target', 'targetAmount', 'targetSales'],
            lastYearSales: ['lastYearSales', 'lastYearAmount', 'samePeriodSales'],
            lastPeriodSales: ['lastPeriodSales', 'lastPeriodAmount', 'sequentialSales'],
            name: ['name', 'productName', 'product'],
            productCode: ['productCode', 'code', 'id'],
            ...options.fieldMappings
        };
    }

    /**
     * 修复产品数据不一致问题
     * @param {Object} product - 产品数据
     * @returns {Object} 修复后的产品数据
     */
    correctProductData(product) {
        if (!product || typeof product !== 'object') {
            console.warn('数据修复器: 无效的产品数据', product);
            return product;
        }

        const corrected = { ...product };
        const corrections = [];

        // 修复字段映射
        Object.keys(this.fieldMappings).forEach(standardField => {
            const possibleFields = this.fieldMappings[standardField];
            const value = this.findFieldValue(product, possibleFields);
            
            if (value !== undefined && corrected[standardField] === undefined) {
                corrected[standardField] = value;
                corrections.push(`映射字段 ${standardField} = ${value}`);
            }
        });

        // 修复数据类型
        const numericFields = ['actual', 'target', 'lastYearSales', 'lastPeriodSales', 'actualQuantity'];
        numericFields.forEach(field => {
            if (corrected[field] !== undefined) {
                const originalValue = corrected[field];
                const numericValue = this.convertToNumber(originalValue);
                
                if (numericValue !== originalValue) {
                    corrected[field] = numericValue;
                    corrections.push(`转换 ${field}: ${originalValue} -> ${numericValue}`);
                }
            }
        });

        // 验证和修复增长率数据
        if (corrected.samePeriodGrowth !== undefined) {
            const fixedGrowth = this.fixGrowthRate(corrected.samePeriodGrowth, corrected.actual, corrected.lastYearSales);
            if (fixedGrowth !== corrected.samePeriodGrowth) {
                corrections.push(`修复同期增长率: ${corrected.samePeriodGrowth} -> ${fixedGrowth}`);
                corrected.samePeriodGrowth = fixedGrowth;
            }
        }

        if (corrected.sequentialGrowth !== undefined) {
            const fixedGrowth = this.fixGrowthRate(corrected.sequentialGrowth, corrected.actual, corrected.lastPeriodSales);
            if (fixedGrowth !== corrected.sequentialGrowth) {
                corrections.push(`修复环比增长率: ${corrected.sequentialGrowth} -> ${fixedGrowth}`);
                corrected.sequentialGrowth = fixedGrowth;
            }
        }

        // 确保必要字段存在
        if (!corrected.name) {
            corrected.name = corrected.productName || corrected.product || '未知产品';
            corrections.push(`设置产品名称: ${corrected.name}`);
        }

        // 设置默认值
        const defaultValues = {
            actual: 0,
            target: 0,
            lastYearSales: 0,
            lastPeriodSales: 0,
            actualQuantity: 0
        };

        Object.keys(defaultValues).forEach(field => {
            if (corrected[field] === undefined || corrected[field] === null) {
                corrected[field] = defaultValues[field];
                corrections.push(`设置默认值 ${field}: ${defaultValues[field]}`);
            }
        });

        // 计算缺失的增长率
        if (!corrected.samePeriodGrowth || corrected.samePeriodGrowth === 'undefined') {
            corrected.samePeriodGrowth = this.calculateGrowthRate(corrected.actual, corrected.lastYearSales);
            corrections.push(`计算同期增长率: ${corrected.samePeriodGrowth}`);
        }

        if (!corrected.sequentialGrowth || corrected.sequentialGrowth === 'undefined') {
            corrected.sequentialGrowth = this.calculateGrowthRate(corrected.actual, corrected.lastPeriodSales);
            corrections.push(`计算环比增长率: ${corrected.sequentialGrowth}`);
        }

        // 记录修复过程
        if (corrections.length > 0 && this.debugMode) {
            console.group(`🔧 数据修复: ${corrected.name}`);
            corrections.forEach(correction => console.log('✓', correction));
            console.groupEnd();
        }

        // 添加修复标记
        if (corrections.length > 0) {
            corrected._corrected = true;
            corrected._corrections = corrections;
        }

        return corrected;
    }

    /**
     * 批量修复产品数据
     * @param {Array} products - 产品数据数组
     * @returns {Array} 修复后的产品数据数组
     */
    correctProductsData(products) {
        if (!Array.isArray(products)) {
            console.warn('数据修复器: 产品数据必须是数组');
            return products;
        }

        const correctedProducts = products.map(product => this.correctProductData(product));
        
        if (this.debugMode) {
            const correctedCount = correctedProducts.filter(p => p._corrected).length;
            console.log(`📊 批量数据修复完成: ${correctedCount}/${products.length} 个产品需要修复`);
        }

        return correctedProducts;
    }

    /**
     * 修复API响应数据
     * @param {Object} response - API响应数据
     * @returns {Object} 修复后的响应数据
     */
    correctAPIResponse(response) {
        if (!response || typeof response !== 'object') {
            return response;
        }

        const corrected = { ...response };
        const corrections = [];

        // 修复基本结构
        if (corrected.success === undefined && corrected.data) {
            corrected.success = true;
            corrections.push('设置success字段为true');
        }

        // 修复产品数据
        if (corrected.data && corrected.data.products) {
            const originalProducts = corrected.data.products;
            corrected.data.products = this.correctProductsData(originalProducts);
            
            const correctedProductsCount = corrected.data.products.filter(p => p._corrected).length;
            if (correctedProductsCount > 0) {
                corrections.push(`修复了 ${correctedProductsCount} 个产品的数据`);
            }
        }

        // 修复汇总数据
        if (corrected.data && corrected.data.summary) {
            const fixedSummary = this.correctSummaryData(corrected.data.summary);
            if (fixedSummary._corrected) {
                corrected.data.summary = fixedSummary;
                corrections.push('修复了汇总数据');
            }
        }

        if (corrections.length > 0) {
            corrected._corrected = true;
            corrected._corrections = corrections;
            
            if (this.debugMode) {
                console.group('🔧 API响应数据修复');
                corrections.forEach(correction => console.log('✓', correction));
                console.groupEnd();
            }
        }

        return corrected;
    }

    /**
     * 修复汇总数据
     * @param {Object} summary - 汇总数据
     * @returns {Object} 修复后的汇总数据
     */
    correctSummaryData(summary) {
        if (!summary || typeof summary !== 'object') {
            return summary;
        }

        const corrected = { ...summary };
        const corrections = [];

        // 确保数值字段为数字类型
        const numericFields = ['totalActual', 'totalTarget', 'totalLastYear', 'totalLastPeriod'];
        numericFields.forEach(field => {
            if (corrected[field] !== undefined) {
                const originalValue = corrected[field];
                const numericValue = this.convertToNumber(originalValue);
                
                if (numericValue !== originalValue) {
                    corrected[field] = numericValue;
                    corrections.push(`转换 ${field}: ${originalValue} -> ${numericValue}`);
                }
            } else {
                corrected[field] = 0;
                corrections.push(`设置默认值 ${field}: 0`);
            }
        });

        // 修复比较数据结构
        if (corrected.samePeriodComparison && typeof corrected.samePeriodComparison === 'object') {
            corrected.samePeriodComparison.current = this.convertToNumber(corrected.samePeriodComparison.current) || 0;
            corrected.samePeriodComparison.previous = this.convertToNumber(corrected.samePeriodComparison.previous) || 0;
        }

        if (corrected.sequentialComparison && typeof corrected.sequentialComparison === 'object') {
            corrected.sequentialComparison.current = this.convertToNumber(corrected.sequentialComparison.current) || 0;
            corrected.sequentialComparison.previous = this.convertToNumber(corrected.sequentialComparison.previous) || 0;
        }

        if (corrections.length > 0) {
            corrected._corrected = true;
            corrected._corrections = corrections;
        }

        return corrected;
    }

    /**
     * 验证数据一致性并自动修复
     * @param {Object} product - 产品数据
     * @returns {Object} 验证和修复结果
     */
    validateAndCorrect(product) {
        const result = {
            original: product,
            corrected: null,
            issues: [],
            fixes: []
        };

        // 检查数据一致性问题
        const issues = this.detectDataIssues(product);
        result.issues = issues;

        if (issues.length > 0 && this.autoCorrect) {
            // 自动修复数据
            result.corrected = this.correctProductData(product);
            result.fixes = result.corrected._corrections || [];
        } else {
            result.corrected = product;
        }

        return result;
    }

    /**
     * 检测数据问题
     * @param {Object} product - 产品数据
     * @returns {Array} 问题列表
     */
    detectDataIssues(product) {
        const issues = [];

        if (!product || typeof product !== 'object') {
            issues.push({ type: 'INVALID_DATA', message: '产品数据无效' });
            return issues;
        }

        // 检查必要字段
        const requiredFields = ['name', 'actual', 'target'];
        requiredFields.forEach(field => {
            if (product[field] === undefined || product[field] === null) {
                issues.push({ 
                    type: 'MISSING_FIELD', 
                    field, 
                    message: `缺少必要字段: ${field}` 
                });
            }
        });

        // 检查数据类型
        const numericFields = ['actual', 'target', 'lastYearSales', 'lastPeriodSales'];
        numericFields.forEach(field => {
            if (product[field] !== undefined && typeof product[field] !== 'number') {
                issues.push({ 
                    type: 'WRONG_TYPE', 
                    field, 
                    expected: 'number', 
                    actual: typeof product[field],
                    message: `字段 ${field} 应该是数字类型` 
                });
            }
        });

        // 检查增长率格式
        if (product.samePeriodGrowth && !this.isValidGrowthRate(product.samePeriodGrowth)) {
            issues.push({ 
                type: 'INVALID_GROWTH_RATE', 
                field: 'samePeriodGrowth', 
                value: product.samePeriodGrowth,
                message: '同期增长率格式无效' 
            });
        }

        if (product.sequentialGrowth && !this.isValidGrowthRate(product.sequentialGrowth)) {
            issues.push({ 
                type: 'INVALID_GROWTH_RATE', 
                field: 'sequentialGrowth', 
                value: product.sequentialGrowth,
                message: '环比增长率格式无效' 
            });
        }

        // 检查数值合理性
        if (product.actual < 0) {
            issues.push({ 
                type: 'NEGATIVE_VALUE', 
                field: 'actual', 
                value: product.actual,
                message: '实际销售额不应为负数' 
            });
        }

        return issues;
    }

    // 私有方法

    /**
     * 查找字段值
     * @param {Object} obj - 对象
     * @param {Array} possibleFields - 可能的字段名数组
     * @returns {*} 字段值
     */
    findFieldValue(obj, possibleFields) {
        for (const field of possibleFields) {
            if (obj[field] !== undefined) {
                return obj[field];
            }
        }
        return undefined;
    }

    /**
     * 转换为数字
     * @param {*} value - 值
     * @returns {number} 数字值
     */
    convertToNumber(value) {
        if (typeof value === 'number') {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 0 : parsed;
        }
        
        return 0;
    }

    /**
     * 修复增长率
     * @param {string} growthRate - 增长率字符串
     * @param {number} current - 当前值
     * @param {number} previous - 之前值
     * @returns {string} 修复后的增长率
     */
    fixGrowthRate(growthRate, current, previous) {
        // 如果增长率格式正确，直接返回
        if (this.isValidGrowthRate(growthRate)) {
            return growthRate;
        }

        // 重新计算增长率
        return this.calculateGrowthRate(current, previous);
    }

    /**
     * 验证增长率格式
     * @param {string} growthRate - 增长率字符串
     * @returns {boolean} 是否有效
     */
    isValidGrowthRate(growthRate) {
        if (growthRate === '-') return true;
        
        // 匹配格式：+123.4% 或 -123.4% 或 123.4%
        const growthPattern = /^[+-]?\d+(\.\d+)?%$/;
        return typeof growthRate === 'string' && growthPattern.test(growthRate);
    }

    /**
     * 计算增长率
     * @param {number} current - 当前值
     * @param {number} previous - 之前值
     * @returns {string} 增长率字符串
     */
    calculateGrowthRate(current, previous) {
        // 情况1：去年同期为0
        if (previous === 0) {
            return "-";
        }

        // 情况2：去年同期为正数（正常情况）
        if (previous > 0) {
            const growthRate = ((current - previous) / previous) * 100;
            return `${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`;
        }

        // 情况3：去年同期为负数（特殊处理）
        if (previous < 0) {
            if (current >= 0) {
                const improvementRate = ((current - previous) / Math.abs(previous)) * 100;
                return `+${improvementRate.toFixed(1)}%`;
            } else {
                const absChange = Math.abs(current) - Math.abs(previous);
                const changeRate = (absChange / Math.abs(previous)) * 100;

                if (absChange > 0) {
                    return `-${changeRate.toFixed(1)}%`;
                } else if (absChange < 0) {
                    return `+${Math.abs(changeRate).toFixed(1)}%`;
                } else {
                    return `0.0%`;
                }
            }
        }

        return "-";
    }

    /**
     * 获取修复统计信息
     * @param {Array} products - 产品数组
     * @returns {Object} 统计信息
     */
    getCorrectionStats(products) {
        if (!Array.isArray(products)) {
            return { total: 0, corrected: 0, issues: [] };
        }

        const correctedProducts = products.filter(p => p._corrected);
        const allIssues = [];

        products.forEach(product => {
            if (product._corrections) {
                allIssues.push(...product._corrections);
            }
        });

        return {
            total: products.length,
            corrected: correctedProducts.length,
            correctionRate: products.length > 0 ? (correctedProducts.length / products.length * 100).toFixed(1) + '%' : '0%',
            totalCorrections: allIssues.length,
            commonIssues: this.getCommonIssues(allIssues)
        };
    }

    /**
     * 获取常见问题
     * @param {Array} corrections - 修复记录数组
     * @returns {Object} 常见问题统计
     */
    getCommonIssues(corrections) {
        const issueCount = {};
        
        corrections.forEach(correction => {
            const issueType = correction.split(':')[0] || correction.split(' ')[0];
            issueCount[issueType] = (issueCount[issueType] || 0) + 1;
        });

        return Object.entries(issueCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .reduce((obj, [key, value]) => {
                obj[key] = value;
                return obj;
            }, {});
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataCorrector;
} else if (typeof window !== 'undefined') {
    window.DataCorrector = DataCorrector;
}