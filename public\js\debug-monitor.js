/**
 * 调试监控器 - 用于记录和监控数据处理过程
 */
class DebugMonitor {
    constructor(options = {}) {
        this.enabled = options.enabled !== false;
        this.logLevel = options.logLevel || 'info'; // 'debug', 'info', 'warn', 'error'
        this.maxLogEntries = options.maxLogEntries || 1000;
        this.logEntries = [];
        this.startTime = Date.now();
        this.keyProducts = options.keyProducts || ['美思信', '思考林', '申捷']; // 重点监控的产品
    }

    /**
     * 记录详细的数据处理过程
     * @param {string} stage - 处理阶段
     * @param {*} data - 数据内容
     * @param {Object} context - 上下文信息
     */
    logDataProcessing(stage, data, context = {}) {
        if (!this.enabled) return;

        const logEntry = {
            timestamp: new Date().toISOString(),
            stage,
            context,
            dataType: Array.isArray(data) ? 'array' : typeof data,
            dataSize: this.getDataSize(data),
            productName: context.productName || 'N/A'
        };

        // 为重点产品添加详细日志
        const isKeyProduct = this.keyProducts.includes(context.productName);
        
        if (this.shouldLog('debug') || isKeyProduct) {
            console.group(`[${stage}] ${context.productName || 'All Products'} - ${new Date().toLocaleTimeString()}`);
            
            if (isKeyProduct) {
                console.log('🔍 重点产品监控:', context.productName);
            }
            
            console.log('处理阶段:', stage);
            console.log('上下文:', context);
            
            if (data && typeof data === 'object') {
                if (Array.isArray(data)) {
                    console.log(`数据数组 (${data.length} 项):`, data.slice(0, 3)); // 只显示前3项
                    if (data.length > 3) {
                        console.log(`... 还有 ${data.length - 3} 项`);
                    }
                } else {
                    console.log('数据对象:', data);
                }
            } else {
                console.log('数据:', data);
            }
            
            console.groupEnd();
        }

        // 存储日志条目
        this.addLogEntry(logEntry);
    }

    /**
     * 检测数据异常
     * @param {Object} product - 产品数据
     * @returns {Array} 异常列表
     */
    detectAnomalies(product) {
        const anomalies = [];

        if (!product || typeof product !== 'object') {
            anomalies.push({
                type: 'INVALID_PRODUCT_DATA',
                product: 'Unknown',
                message: '产品数据格式无效'
            });
            return anomalies;
        }

        // 检查同期和环比当期数据是否一致
        if (product.samePeriodCurrent !== undefined && 
            product.sequentialCurrent !== undefined &&
            product.samePeriodCurrent !== product.sequentialCurrent) {
            anomalies.push({
                type: 'CURRENT_DATA_MISMATCH',
                product: product.name,
                samePeriod: product.samePeriodCurrent,
                sequential: product.sequentialCurrent,
                difference: Math.abs(product.samePeriodCurrent - product.sequentialCurrent),
                message: '同期和环比的当期数据不一致'
            });
        }

        // 检查实际销售额与当期数据的一致性
        if (product.actual !== undefined && product.samePeriodCurrent !== undefined) {
            const difference = Math.abs(product.actual - product.samePeriodCurrent);
            if (difference > 0.01) { // 允许小数点精度误差
                anomalies.push({
                    type: 'ACTUAL_VS_CURRENT_MISMATCH',
                    product: product.name,
                    actual: product.actual,
                    samePeriodCurrent: product.samePeriodCurrent,
                    difference: difference,
                    message: '实际销售额与同期当期数据不一致'
                });
            }
        }

        // 检查增长率计算异常
        if (product.actual !== undefined && product.lastYearSales !== undefined && 
            product.samePeriodGrowth !== undefined && product.samePeriodGrowth !== '-') {
            
            const expectedGrowth = product.lastYearSales > 0 ? 
                ((product.actual - product.lastYearSales) / product.lastYearSales * 100) : 0;
            const actualGrowth = this.parseGrowthRate(product.samePeriodGrowth);
            
            if (Math.abs(expectedGrowth - actualGrowth) > 0.1) {
                anomalies.push({
                    type: 'GROWTH_RATE_CALCULATION_ERROR',
                    product: product.name,
                    expectedGrowth: expectedGrowth.toFixed(1) + '%',
                    actualGrowth: product.samePeriodGrowth,
                    message: '同期增长率计算异常'
                });
            }
        }

        // 检查数据缺失
        const requiredFields = ['name', 'actual', 'target'];
        const missingFields = requiredFields.filter(field => 
            product[field] === undefined || product[field] === null
        );
        
        if (missingFields.length > 0) {
            anomalies.push({
                type: 'MISSING_REQUIRED_FIELDS',
                product: product.name || 'Unknown',
                missingFields: missingFields,
                message: `缺少必要字段: ${missingFields.join(', ')}`
            });
        }

        // 记录异常
        if (anomalies.length > 0) {
            this.logAnomalies(product.name, anomalies);
        }

        return anomalies;
    }

    /**
     * 记录异常信息
     * @param {string} productName - 产品名称
     * @param {Array} anomalies - 异常列表
     */
    logAnomalies(productName, anomalies) {
        if (!this.enabled) return;

        console.group(`⚠️ 数据异常检测 - ${productName}`);
        anomalies.forEach((anomaly, index) => {
            console.warn(`异常 ${index + 1}:`, anomaly.message);
            console.log('详细信息:', anomaly);
        });
        console.groupEnd();

        // 存储异常记录
        this.addLogEntry({
            timestamp: new Date().toISOString(),
            type: 'ANOMALY_DETECTION',
            productName,
            anomalies,
            level: 'warn'
        });
    }

    /**
     * 监控API调用性能
     * @param {string} apiName - API名称
     * @param {Function} apiCall - API调用函数
     * @returns {Promise} API调用结果
     */
    async monitorAPICall(apiName, apiCall) {
        const startTime = performance.now();
        
        this.logDataProcessing('API_CALL_START', null, { 
            apiName, 
            startTime: new Date().toISOString() 
        });

        try {
            const result = await apiCall();
            const endTime = performance.now();
            const duration = endTime - startTime;

            this.logDataProcessing('API_CALL_SUCCESS', null, {
                apiName,
                duration: duration.toFixed(2) + 'ms',
                endTime: new Date().toISOString()
            });

            // 性能警告
            if (duration > 5000) {
                console.warn(`⚠️ API调用 ${apiName} 耗时过长: ${duration.toFixed(2)}ms`);
            }

            return result;
        } catch (error) {
            const endTime = performance.now();
            const duration = endTime - startTime;

            this.logDataProcessing('API_CALL_ERROR', error, {
                apiName,
                duration: duration.toFixed(2) + 'ms',
                error: error.message,
                endTime: new Date().toISOString()
            });

            throw error;
        }
    }

    /**
     * 监控数据处理流程
     * @param {string} flowName - 流程名称
     * @param {Array} steps - 处理步骤
     * @param {*} initialData - 初始数据
     */
    async monitorDataFlow(flowName, steps, initialData) {
        if (!this.enabled) return initialData;

        console.group(`🔄 数据流程监控: ${flowName}`);
        
        let currentData = initialData;
        const flowStartTime = performance.now();

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            const stepStartTime = performance.now();
            
            try {
                console.log(`步骤 ${i + 1}: ${step.name}`);
                currentData = await step.execute(currentData);
                
                const stepDuration = performance.now() - stepStartTime;
                console.log(`✅ 步骤完成，耗时: ${stepDuration.toFixed(2)}ms`);
                
                // 记录步骤结果
                this.logDataProcessing(`FLOW_STEP_${i + 1}`, currentData, {
                    flowName,
                    stepName: step.name,
                    stepIndex: i + 1,
                    duration: stepDuration.toFixed(2) + 'ms'
                });
                
            } catch (error) {
                const stepDuration = performance.now() - stepStartTime;
                console.error(`❌ 步骤失败: ${step.name}`, error);
                
                this.logDataProcessing(`FLOW_STEP_ERROR_${i + 1}`, error, {
                    flowName,
                    stepName: step.name,
                    stepIndex: i + 1,
                    duration: stepDuration.toFixed(2) + 'ms',
                    error: error.message
                });
                
                throw error;
            }
        }

        const totalDuration = performance.now() - flowStartTime;
        console.log(`🏁 流程完成，总耗时: ${totalDuration.toFixed(2)}ms`);
        console.groupEnd();

        return currentData;
    }

    /**
     * 获取性能统计信息
     * @returns {Object} 性能统计
     */
    getPerformanceStats() {
        const now = Date.now();
        const uptime = now - this.startTime;
        
        const apiCalls = this.logEntries.filter(entry => 
            entry.stage && entry.stage.startsWith('API_CALL')
        );
        
        const successfulCalls = apiCalls.filter(entry => 
            entry.stage === 'API_CALL_SUCCESS'
        );
        
        const failedCalls = apiCalls.filter(entry => 
            entry.stage === 'API_CALL_ERROR'
        );

        const anomalies = this.logEntries.filter(entry => 
            entry.type === 'ANOMALY_DETECTION'
        );

        return {
            uptime: uptime,
            totalLogEntries: this.logEntries.length,
            apiCalls: {
                total: apiCalls.length / 2, // 每次调用有开始和结束两条记录
                successful: successfulCalls.length,
                failed: failedCalls.length,
                successRate: successfulCalls.length > 0 ? 
                    (successfulCalls.length / (successfulCalls.length + failedCalls.length) * 100).toFixed(1) + '%' : '0%'
            },
            anomalies: {
                total: anomalies.length,
                byProduct: this.groupAnomaliesByProduct(anomalies)
            }
        };
    }

    /**
     * 导出调试日志
     * @param {string} format - 导出格式 ('json' 或 'csv')
     * @returns {string} 导出的日志数据
     */
    exportLogs(format = 'json') {
        if (format === 'json') {
            return JSON.stringify({
                exportTime: new Date().toISOString(),
                performanceStats: this.getPerformanceStats(),
                logEntries: this.logEntries
            }, null, 2);
        } else if (format === 'csv') {
            const headers = ['timestamp', 'stage', 'productName', 'dataType', 'dataSize', 'level'];
            const rows = this.logEntries.map(entry => [
                entry.timestamp,
                entry.stage || entry.type,
                entry.productName || entry.context?.productName || '',
                entry.dataType || '',
                entry.dataSize || '',
                entry.level || 'info'
            ]);
            
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }
        
        return '';
    }

    // 私有方法

    /**
     * 判断是否应该记录日志
     * @param {string} level - 日志级别
     * @returns {boolean} 是否记录
     */
    shouldLog(level) {
        const levels = { debug: 0, info: 1, warn: 2, error: 3 };
        return levels[level] >= levels[this.logLevel];
    }

    /**
     * 获取数据大小
     * @param {*} data - 数据
     * @returns {string} 数据大小描述
     */
    getDataSize(data) {
        if (Array.isArray(data)) {
            return `${data.length} items`;
        } else if (typeof data === 'object' && data !== null) {
            return `${Object.keys(data).length} properties`;
        } else if (typeof data === 'string') {
            return `${data.length} chars`;
        }
        return 'N/A';
    }

    /**
     * 添加日志条目
     * @param {Object} entry - 日志条目
     */
    addLogEntry(entry) {
        this.logEntries.push(entry);
        
        // 限制日志条目数量
        if (this.logEntries.length > this.maxLogEntries) {
            this.logEntries = this.logEntries.slice(-this.maxLogEntries);
        }
    }

    /**
     * 解析增长率字符串
     * @param {string} growthRate - 增长率字符串
     * @returns {number} 增长率数值
     */
    parseGrowthRate(growthRate) {
        if (growthRate === '-') return 0;
        const numericPart = growthRate.replace(/[+%]/g, '');
        return parseFloat(numericPart) || 0;
    }

    /**
     * 按产品分组异常
     * @param {Array} anomalies - 异常列表
     * @returns {Object} 按产品分组的异常
     */
    groupAnomaliesByProduct(anomalies) {
        const grouped = {};
        anomalies.forEach(entry => {
            const productName = entry.productName || 'Unknown';
            if (!grouped[productName]) {
                grouped[productName] = 0;
            }
            grouped[productName] += entry.anomalies ? entry.anomalies.length : 1;
        });
        return grouped;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DebugMonitor;
} else if (typeof window !== 'undefined') {
    window.DebugMonitor = DebugMonitor;
}